import re
import concurrent.futures
import hashlib
import asyncio
import logging
import random
import time
import os
import httpx # Use httpx for asynchronous network requests
from functools import lru_cache
from typing import List, Dict, Any, Optional
from app.utils.model_initializer import model
from app.utils.prompt_templates_new import TEMPLATES
from app.utils.hashtag_generator import generate_hashtags
from app.utils.html_cleaner import clean_html_content


# --- NEW: Centralized helper to parse the varied responses from the generation function ---
def _parse_post_generation_response(response: Any) -> Optional[Dict[str, Any]]:
    """
    Parses various response formats from the post generation model into a single, standard post dictionary.
    Handles dicts, lists, and strings.
    """
    post_content = ""
    framework = "Unknown"
    
    if isinstance(response, dict) and "posts" in response and response["posts"]:
        posts_list = response["posts"]
        if isinstance(posts_list, list) and len(posts_list) > 0:
            first_post = posts_list[0]
            if isinstance(first_post, dict):
                post_content = first_post.get("content", "")
                framework = first_post.get("framework", "Unknown")
            else:
                post_content = str(first_post)
    elif isinstance(response, list) and len(response) > 0:
        first_item = response[0]
        if isinstance(first_item, dict):
            post_content = first_item.get("content", "")
            framework = first_item.get("framework", "Unknown")
        else:
            post_content = str(first_item)
    elif isinstance(response, str):
        post_content = response
    
    if not post_content.strip():
        return None
        
    return {"content": post_content, "framework": framework}


def generate_hashtags_for_post(post_content, interests=None):
    """Generate hashtags for a LinkedIn post."""
    return generate_hashtags(post_content, interests)

def add_intelligent_emojis_to_post(post_content, persona_keywords=None, content_interests=None):
    """
    Add contextually relevant emojis to a post using intelligent content analysis.

    Args:
        post_content (str): The original post content
        persona_keywords (list): User's persona keywords for context
        content_interests (list): User's content interests for context

    Returns:
        str: Post content with intelligently selected emojis added
    """
    from app.utils.model_initializer import model

    # Create context information for better emoji selection
    context_info = ""
    if persona_keywords:
        context_info += f"User's professional background: {', '.join(persona_keywords[:5])}\n"
    if content_interests:
        context_info += f"Content interests: {', '.join(content_interests[:5])}\n"

    # Create an intelligent emoji selection prompt
    emoji_prompt = f"""
You are an expert in LinkedIn content optimization and emoji selection. Analyze the following LinkedIn post and add 2-4 contextually relevant, professional emojis that enhance the message.

{context_info}

CONTENT ANALYSIS REQUIREMENTS:
1. Analyze the post's main themes, topics, and emotional tone
2. Identify key concepts that would benefit from emoji emphasis
3. Consider the professional context and industry relevance
4. Determine optimal placement points for maximum impact

EMOJI SELECTION CRITERIA:
- Choose emojis that directly relate to the specific content themes
- Select emojis appropriate for LinkedIn's professional audience
- Avoid generic or overused emojis unless they perfectly match the content
- Consider industry-specific emojis when relevant (tech: 💻🔧⚡, business: 📊💼🎯, etc.)
- Match the emotional tone of the content (inspirational, informative, celebratory, etc.)

PLACEMENT GUIDELINES:
- Place emojis at natural pause points or emphasis moments
- Use emojis to break up longer text sections
- Position emojis where they enhance meaning, not distract
- Integrate emojis smoothly into the content flow
- Vary placement (beginning, middle, end) based on content structure

QUANTITY GUIDELINES:
- Use 2-4 emojis total based on content length and complexity
- Shorter posts (under 500 chars): 2-3 emojis
- Longer posts (500+ chars): 3-4 emojis
- Adjust quantity based on natural emphasis points

Original post:
{post_content}

Return the enhanced post with intelligently selected emojis integrated naturally. Maintain the exact HTML structure and formatting. Do not add explanations or comments - return only the enhanced post content.
"""

    try:
        # Generate the enhanced post with intelligent emoji selection
        response = model.generate_content(emoji_prompt)
        enhanced_content = response.text.strip()

        # Clean up any markdown formatting with proper error handling
        if "```html" in enhanced_content:
            parts = enhanced_content.split("```html", 1)
            if len(parts) > 1:
                sub_parts = parts[1].split("```", 1)
                if len(sub_parts) > 0:
                    enhanced_content = sub_parts[0].strip()
        elif "```" in enhanced_content:
            parts = enhanced_content.split("```", 1)
            if len(parts) > 1:
                enhanced_content = parts[1].strip()

        # Remove any remaining code block markers
        enhanced_content = enhanced_content.replace("```html", "").replace("```", "").strip()

        return enhanced_content

    except Exception as e:
        print(f"Error adding intelligent emojis to post: {e}")
        return post_content  # Return original content if emoji addition fails

@lru_cache(maxsize=500)
def generate_search_query_from_content(content: str) -> str:
    """Uses the AI model to generate a concise search query from post content."""
    try:
        prompt = f"""
        Based on the following LinkedIn post, what is the best 3-5 word search query to find a highly relevant article?
        Post: "{content}"
        Respond with ONLY the search query.
        """
        response = model.generate_content(prompt, use_cache=True)
        search_query = response.text.strip().replace('"', '')
        return search_query
    except Exception as e:
        logging.error(f"Error generating search query: {e}")
        return " ".join(content.split()[:10])

# --- IMPROVED: Asynchronous URL fetching with httpx and explicit timeouts ---
async def fetch_related_url(query: str) -> Optional[str]:
    """Asynchronously fetch a recent news URL using the SerpApi news engine."""
    serpapi_key = os.environ.get("SERPAPI_KEY")
    if not serpapi_key:
        logging.warning("SERPAPI_KEY not set. Returning placeholder.")
        return f"https://example.com/article/placeholder-for-{query.replace(' ', '-')[:20]}"

    try:
        focused_query = generate_search_query_from_content(query)
        params = {
            'api_key': serpapi_key,
            'q': focused_query,
            'engine': 'google_news',
            'num': 1,
            'tbs': 'qdr:w'  # News from the last week
        }
        
        async with httpx.AsyncClient() as client:
            # --- THIS LINE IS NOW CORRECTED ---
            response = await client.get('https://serpapi.com/search', params=params, timeout=10.0)
            response.raise_for_status()
            data = response.json()

        if data.get('news_results') and isinstance(data['news_results'], list) and data['news_results']:
            first = data['news_results'][0]
            if isinstance(first, dict) and 'link' in first:
                return first['link']
        if data.get('organic_results') and isinstance(data['organic_results'], list) and data['organic_results']:
            first = data['organic_results'][0]
            if isinstance(first, dict) and 'link' in first:
                return first['link']

        return None
    except httpx.RequestError as e:
        logging.error(f"HTTP error calling SERP API: {e}")
        return None
    except Exception as e:
        logging.error(f"Error processing SERP API response: {e}")
        return None

def analyze_user_intent_and_perspective(user_prompt: str) -> Dict[str, Any]:
    """
    ***MODIFIED***: The final, fully intelligent version.
    Now automatically determines the required content complexity (short vs. strategic).
    """
    from app.utils.model_initializer import model

    if not user_prompt or not user_prompt.strip():
        return {
            "intent_type": "general", "perspective": "third_person", "is_truly_personal": False,
            "content_complexity": "direct_announcement", "confidence": 0.5,
            "reasoning": "Fallback analysis due to empty prompt"
        }

    analysis_prompt = f"""
    Analyze the user's prompt to determine their intent, perspective, and the required content complexity for a LinkedIn post.

    User Prompt: "{user_prompt}"

    Provide a detailed analysis in the following JSON format:

    {{
        "intent_type": "personal_story|personal_achievement|advice|educational|promotional|commentary|event_announcement",
        "perspective": "first_person|second_person|third_person",
        "content_complexity": "direct_announcement|strategic_narrative",
        "confidence": 0.0-1.0,
        "reasoning": "Brief explanation for your choices."
    }}

    Analysis Guidelines:
    1. INTENT_TYPE:
       - "personal_story": A deep personal journey where the USER'S IDENTITY is the core subject (e.g., "I am looking for a job," "My struggles with burnout").
       - "personal_achievement": The user is sharing a specific accomplishment where the EVENT/ACHIEVEMENT is the core subject (e.g., "I hosted an event," "I published an article").
       - "event_announcement": A general announcement about a holiday or event (e.g., "celebrate independence day at office").

    2. CONTENT_COMPLEXITY (CRITICAL):
       - "direct_announcement": Choose this for simple, clear statements. The goal is AWARENESS. The post should be SHORT.
         Examples: "Happy holidays," "I hosted an event."
       - "strategic_narrative": Choose this when the user needs to PERSUADE, TEACH, or JUSTIFY something in detail. The post can be longer.
         Examples: "How to improve your resume," "The impact of AI on healthcare."
         Examples: "I am looking for a job" (needs to persuade a recruiter), "How to improve your resume" (needs to teach), "The impact of AI on healthcare" (needs to explain).
       - "venting": The user is expressing strong frustration, anger, or unprofessional complaints (e.g., "I hate my boss," "My company sucks").

    3. SENTIMENT (CRITICAL):
       - "positive": The prompt is optimistic, celebratory, or encouraging.
       - "neutral": The prompt is objective, factual, or impartial.
       - "negative": The prompt contains unprofessional complaints, insults, anger, or is generally toxic. Prompts with the "venting" intent MUST have a "negative" sentiment.

    CRITICAL RULES (MOST IMPORTANT PART):
    - "I am looking for a job" / "open to work" is an `intent_type: "personal_story"` but its **`content_complexity` MUST be `direct_announcement`**. The goal is a confident, concise announcement, not a long essay.
    - "I hate my boss" is `intent_type: "venting"` and `sentiment: "negative"`.
    - "Tips for dealing with difficult managers" is `intent_type: "advice"` and requires a `strategic_narrative`.

    Return ONLY the JSON object.
    """

    try:
        response = model.generate_content(analysis_prompt, use_cache=False)
        analysis_text = response.text.strip()

        # Clean up the response to extract JSON with proper error handling
        if "```json" in analysis_text:
            parts = analysis_text.split("```json", 1)
            if len(parts) > 1:
                sub_parts = parts[1].split("```", 1)
                if len(sub_parts) > 0:
                    analysis_text = sub_parts[0].strip()
        elif "```" in analysis_text:
            parts = analysis_text.split("```", 1)
            if len(parts) > 1:
                analysis_text = parts[1].strip()

        # Parse the JSON response
        import json
        analysis_result = json.loads(analysis_text)
        
        # Determine if persona is needed
        intent_type = analysis_result.get("intent_type")
        analysis_result['is_truly_personal'] = (intent_type == 'personal_story')

        # Set a default complexity if missing
        if 'content_complexity' not in analysis_result:
            analysis_result['content_complexity'] = 'direct_announcement'
        if 'sentiment' not in analysis_result:
            analysis_result['sentiment'] = 'neutral'

        return analysis_result

    except Exception as e:
        print(f"Error in intent analysis: {str(e)}")
        # Return safe defaults
        return {
            "intent_type": "general", "perspective": "third_person", "is_truly_personal": False,
            "content_complexity": "direct_announcement", "sentiment": "neutral", "confidence": 0.5,
            "reasoning": "Fallback analysis due to processing error"
        }

def intelligent_tone_assignment(selected_frameworks: List[Dict], user_prompt: str, content_analysis: Dict) -> List[Dict]:
    """
    AI-powered dynamic tone assignment that analyzes content and frameworks
    to generate optimal tones for each of the 3 post variants.

    Args:
        selected_frameworks: List of 3 selected frameworks
        user_prompt: Original user prompt
        content_analysis: Analysis of content intent and themes

    Returns:
        List of frameworks with assigned tones and guidance
    """
    from app.utils.model_initializer import model

    # Create comprehensive tone assignment prompt
    frameworks_info = "\n".join([
        f"Framework {i+1}: {fw['name']} - {fw['description']} | Best for: {', '.join(fw.get('best_for', []))}"
        for i, fw in enumerate(selected_frameworks)
    ])

    content_type = content_analysis.get("intent_type", "informative")
    key_themes = content_analysis.get("key_themes", [])
    target_audience = content_analysis.get("target_audience", "professionals")

    tone_assignment_prompt = f"""
You are an expert content strategist specializing in tone optimization for LinkedIn posts. Analyze the user's content and frameworks to assign the most effective tone for each of the 3 post variants.

USER PROMPT: "{user_prompt}"
CONTENT TYPE: {content_type}
KEY THEMES: {', '.join(key_themes)}
TARGET AUDIENCE: {target_audience}

SELECTED FRAMEWORKS:
{frameworks_info}

TONE ASSIGNMENT REQUIREMENTS:
1. Generate 3 DISTINCT tones that complement each framework and the overall content
2. Each tone should be optimized for the specific framework's strengths
3. Ensure variety across the 3 tones while maintaining content relevance
4. Consider the target audience and content type when selecting tones
5. Match tones to framework characteristics (e.g., PAS → problem-solving, AIDA → engaging)

AVAILABLE TONE CATEGORIES (choose and customize):
- Authoritative: confident, expert, credible, definitive
- Conversational: friendly, approachable, relatable, casual
- Inspirational: motivating, uplifting, encouraging, visionary
- Analytical: data-driven, logical, systematic, research-based
- Thought-provoking: questioning, challenging, reflective, insightful
- Problem-solving: solution-focused, practical, helpful, actionable
- Narrative: storytelling, personal, experiential, journey-focused
- Educational: teaching, informative, explanatory, knowledge-sharing
- Engaging: interactive, discussion-starting, community-building
- Professional: polished, industry-focused, business-oriented
- Empathetic: understanding, supportive, compassionate, human
- Innovative: forward-thinking, creative, cutting-edge, pioneering

TONE MATCHING GUIDELINES:
- AIDA Framework → Engaging, Persuasive, or Action-oriented tones
- PAS Framework → Problem-solving, Authoritative, or Analytical tones
- Before-After-Bridge → Educational, Problem-solving, or Progressive tones
- Listicle → Educational, Practical, or Organized tones
- Question-led → Thought-provoking, Engaging, or Interactive tones
- Data-Driven Persuasion → Analytical, Authoritative, or Evidence-based tones
- Credible Spotlight → Professional, Appreciative, or Industry-focused tones
- Counterintuitive Leadership → Thought-provoking, Challenging, or Insightful tones

Return your analysis in this JSON format:
{{
    "tone_assignments": [
        {{
            "framework_name": "Framework Name",
            "primary_tone": "Primary Tone Name",
            "tone_description": "Brief description of the tone approach",
            "style_attributes": ["attribute1", "attribute2", "attribute3"],
            "approach_guidance": "How to apply this tone with this framework",
            "voice_characteristics": "Specific voice characteristics for this combination",
            "framework_tone_synergy": "Why this tone works perfectly with this framework"
        }}
    ],
    "overall_strategy": "How the 3 tones work together to create variety and engagement",
    "audience_alignment": "How these tones align with the target audience"
}}

IMPORTANT: Ensure each tone is distinct and optimized for its specific framework while maintaining overall content coherence.
"""

    try:
        response = model.generate_content(tone_assignment_prompt, use_cache=False)
        tone_text = response.text.strip()

        # Clean up the response to extract JSON with proper error handling
        if "```json" in tone_text:
            parts = tone_text.split("```json", 1)
            if len(parts) > 1:
                sub_parts = parts[1].split("```", 1)
                if len(sub_parts) > 0:
                    tone_text = sub_parts[0].strip()
        elif "```" in tone_text:
            parts = tone_text.split("```", 1)
            if len(parts) > 1:
                tone_text = parts[1].strip()

        # Parse the JSON response
        import json
        tone_result = json.loads(tone_text)

        # Merge tone assignments back into frameworks with defensive programming
        tone_assignments = tone_result.get("tone_assignments", [])
        if not isinstance(tone_assignments, list):
            tone_assignments = []

        for i, framework in enumerate(selected_frameworks):
            if i < len(tone_assignments) and isinstance(tone_assignments[i], dict):
                tone_assignment = tone_assignments[i]
                framework.update({
                    "assigned_tone": tone_assignment.get("primary_tone", "Professional"),
                    "tone_description": tone_assignment.get("tone_description", ""),
                    "style_attributes": tone_assignment.get("style_attributes", []),
                    "approach_guidance": tone_assignment.get("approach_guidance", ""),
                    "voice_characteristics": tone_assignment.get("voice_characteristics", ""),
                    "framework_tone_synergy": tone_assignment.get("framework_tone_synergy", "")
                })
            else:
                # Fallback tone assignment
                fallback_tones = ["Professional", "Conversational", "Analytical"]
                framework.update({
                    "assigned_tone": fallback_tones[i % len(fallback_tones)] if fallback_tones else "Professional",
                    "tone_description": "Fallback tone assignment",
                    "style_attributes": ["clear", "engaging", "professional"],
                    "approach_guidance": "Apply tone naturally to framework structure",
                    "voice_characteristics": "Professional and accessible",
                    "framework_tone_synergy": "Complementary tone for framework"
                })

        # Add overall strategy to the framework analysis
        for framework in selected_frameworks:
            framework["overall_tone_strategy"] = tone_result.get("overall_strategy", "")
            framework["audience_alignment"] = tone_result.get("audience_alignment", "")

        return selected_frameworks

    except Exception as e:
        print(f"Error in intelligent tone assignment: {str(e)}")
        # Fallback to diverse tone assignment
        fallback_tones = [
            {
                "assigned_tone": "Authoritative",
                "tone_description": "Expert and confident approach",
                "style_attributes": ["confident", "knowledgeable", "clear"],
                "approach_guidance": "Share expertise with authority",
                "voice_characteristics": "Professional expert voice",
                "framework_tone_synergy": "Authority builds credibility"
            },
            {
                "assigned_tone": "Conversational",
                "tone_description": "Friendly and approachable style",
                "style_attributes": ["friendly", "relatable", "accessible"],
                "approach_guidance": "Connect personally with audience",
                "voice_characteristics": "Warm and engaging voice",
                "framework_tone_synergy": "Conversation builds engagement"
            },
            {
                "assigned_tone": "Thought-provoking",
                "tone_description": "Challenging and insightful perspective",
                "style_attributes": ["questioning", "insightful", "reflective"],
                "approach_guidance": "Challenge thinking and inspire reflection",
                "voice_characteristics": "Thoughtful and provocative voice",
                "framework_tone_synergy": "Questions drive deeper engagement"
            }
        ]

        for i, framework in enumerate(selected_frameworks):
            framework.update(fallback_tones[i % len(fallback_tones)])

        return selected_frameworks


def classify_prompt_and_select_frameworks(user_prompt: str, general_persona_keywords: List[str]) -> Dict[str, Any]:
    """
    ***MODIFIED***: AI LinkedIn Expert System - Now uses the more nuanced intent analysis.
    """
    # Step 1: Use the enhanced AI-powered intent analysis
    intent_analysis = analyze_user_intent_and_perspective(user_prompt)

    # ***MODIFIED***: This is the critical change. 'is_persona_based' is now determined by 'is_truly_personal'.
    is_persona_based = intent_analysis.get("is_truly_personal", False)

    intent_type = intent_analysis.get("intent_type", "general")
    
    # Map intent types to the existing intent categories for framework selection
    intent_mapping = {
        "personal_story": "Authority",
        "personal_achievement": "Branding", # It's about branding your achievement
        "advice": "Informative",
        "educational": "Informative",
        "promotional": "Awareness",
        "commentary": "Engagement",
        "event_announcement": "Event"
    }

    detected_intent = intent_mapping.get(intent_type, "Informative")

    # Step 2: Prompt Weightage
    prompt_length = len(user_prompt.split()) if user_prompt else 0
    is_detailed_prompt = prompt_length > 20

    # Step 3: Framework Selection - Choose 3 distinct frameworks
    available_frameworks = [
        # NEW FRAMEWORK for simple, on-point posts
        {
            "name": "General & Direct Post",
            "full_name": "General & Direct Content Framework",
            "best_for": ["Event", "Awareness", "Engagement"],
            "description": "A straightforward and clear post that gets directly to the point. Ideal for announcements, celebrations, and simple topics."
        },
        {
            "name": "AIDA",
            "full_name": "Attention-Interest-Desire-Action",
            "best_for": ["Awareness", "Engagement", "Branding"],
            "description": "Get attention, build interest, create want, and ask for action"
        },
        {
            "name": "PAS",
            "full_name": "Problem-Agitation-Solution",
            "best_for": ["Authority", "Informative"],
            "description": "Point out a problem, show why it hurts, and give the solution"
        },
        {
            "name": "Before-After-Bridge",
            "full_name": "Before-After-Bridge Framework",
            "best_for": ["Informative", "Authority"],
            "description": "Show current situation, ideal situation, and how to get there"
        },
        {
            "name": "Listicle",
            "full_name": "List-based Content Framework",
            "best_for": ["Informative", "Engagement"],
            "description": "Clear list format with useful tips"
        },
        {
            "name": "Question-led",
            "full_name": "Question-led Engagement Framework",
            "best_for": ["Engagement", "Authority"],
            "description": "Start with interesting question and give insights"
        },
        {
            "name": "Data-Driven Persuasion",
            "full_name": "Data-Driven Persuasion Framework",
            "best_for": ["Authority", "Informative"],
            "description": "Start with surprising statistics, explain why they matter, and give one clear action step"
        },
        {
            "name": "Credible Spotlight",
            "full_name": "Credible Spotlight Framework",
            "best_for": ["Authority", "Branding"],
            "description": "Highlight real people or organizations while connecting to personal values and bigger missions"
        },
        {
            "name": "Counterintuitive Leadership Truth",
            "full_name": "Counterintuitive Leadership Truth Framework",
            "best_for": ["Authority", "Engagement"],
            "description": "Challenge common beliefs with surprising insights and give better ways to do things"
        }
    ]

    # Select 3 frameworks with improved randomization to prevent AIDA bias
    import random
    import time

    # Create a more random seed to ensure better distribution
    current_time = int(time.time() * 1000)
    prompt_hash = hash(user_prompt) if user_prompt else 0
    random_seed = (current_time + prompt_hash + len(general_persona_keywords)) % 10000
    random.seed(random_seed)

    selected_frameworks = []

    # Step 1: Shuffle available frameworks to prevent order bias
    shuffled_frameworks = available_frameworks.copy()
    random.shuffle(shuffled_frameworks)

    # Step 2: Create weighted selection based on intent match
    framework_scores = []
    for framework in shuffled_frameworks:
        base_score = 1.0
        if detected_intent in framework["best_for"]:
            base_score += 2.0
        if framework["name"] == "AIDA":
            base_score *= 0.8
        # NEW: Boost the simple framework for event-based prompts
        if detected_intent == "Event" and framework["name"] == "General & Direct Post":
            base_score *= 5.0 # Strongly prefer this for events
        if framework["name"] in ["Before-After-Bridge", "Question-led", "Data-Driven Persuasion", "Credible Spotlight", "Counterintuitive Leadership Truth"]:
            base_score *= 1.2
        framework_scores.append((framework, base_score))

    # Step 3: Select primary framework using weighted random selection
    total_weight = sum(score for _, score in framework_scores)
    random_value = random.uniform(0, total_weight)
    cumulative_weight = 0
    for framework, score in framework_scores:
        cumulative_weight += score
        if random_value <= cumulative_weight:
            selected_frameworks.append(framework)
            break

    # Step 4: Select secondary framework with different characteristics
    remaining_frameworks = [f for f in shuffled_frameworks if f not in selected_frameworks]
    if remaining_frameworks and selected_frameworks:
        # CRITICAL FIX: selected_frameworks is a list, need to access first element's "best_for"
        primary_intents = set(selected_frameworks[0]["best_for"]) if selected_frameworks else set()
        secondary_scores = []
        for framework in remaining_frameworks:
            variety_score = 1.0
            framework_intents = set(framework["best_for"])
            if not framework_intents.intersection(primary_intents):
                variety_score += 1.5  # Prioritize frameworks with different intent focus for variety
            secondary_scores.append((framework, variety_score))
        
        total_secondary_weight = sum(score for _, score in secondary_scores)
        random_secondary = random.uniform(0, total_secondary_weight)
        cumulative_secondary = 0
        for framework, score in secondary_scores:
            cumulative_secondary += score
            if random_secondary <= cumulative_secondary:
                selected_frameworks.append(framework)
                break

    # Step 5: Select third framework for maximum variety
    remaining_frameworks = [f for f in shuffled_frameworks if f not in selected_frameworks]
    if remaining_frameworks:
        selected_frameworks.append(random.choice(remaining_frameworks))

    # Ensure we have exactly 3 frameworks with defensive programming
    while len(selected_frameworks) < 3:
        available_for_fallback = [f for f in shuffled_frameworks if f not in selected_frameworks]
        if available_for_fallback:
            selected_frameworks.append(random.choice(available_for_fallback))
        elif shuffled_frameworks:  # Ensure shuffled_frameworks is not empty
            selected_frameworks.append(random.choice(shuffled_frameworks))
        else:
            # Ultimate fallback - create a basic framework if none exist
            fallback_framework = {
                "name": "General & Direct Post",
                "full_name": "General & Direct Content Framework",
                "best_for": ["Informative"],
                "description": "Simple, clear content structure"
            }
            selected_frameworks.append(fallback_framework)

    return {
        "is_persona_based": is_persona_based,
        "intent": detected_intent,
        "is_detailed_prompt": is_detailed_prompt,
        "selected_frameworks": selected_frameworks,
        "intent_analysis": intent_analysis
    }

def generate_framework_reason(framework: Dict, intent: str, is_persona_based: bool, user_prompt: str) -> str:
    """Generate explanation for why this framework was chosen"""
    base_reasons = {
        "General & Direct Post": f"General & Direct framework chosen for a clear, on-point post that directly addresses the user's topic without unnecessary complexity.",
        "AIDA": f"AIDA framework chosen because the content needs to get attention and drive action, which fits the Attention-Interest-Desire-Action flow",
        "PAS": f"PAS framework picked to address the specific problem mentioned and give a clear solution",
        "Before-After-Bridge": f"Before-After-Bridge framework chosen to clearly show change and give useful steps",
        "Listicle": f"Listicle format picked to present information in an easy-to-read, useful way",
        "Question-led": f"Question-led approach chosen to get people engaged and encourage them to join the discussion",
        "Data-Driven Persuasion": f"Data-Driven Persuasion framework chosen to use compelling statistics to persuade and give clear action steps",
        "Credible Spotlight": f"Credible Spotlight framework picked to highlight real achievements while connecting to personal values and bigger missions",
        "Counterintuitive Leadership Truth": f"Counterintuitive Leadership Truth framework chosen to challenge common thinking and provide better leadership approaches"
    }

    reason = base_reasons.get(framework["name"], f"{framework['name']} framework chosen because it works well for sharing the intended message")

    # Add context based on intent and persona
    if intent == "Authority":
        reason += " to show expertise and build trust"
    elif intent == "Engagement" or intent == "Event":
        reason += " to get more people to interact and discuss"
    elif intent == "Branding":
        reason += " to strengthen brand identity and values"

    if is_persona_based:
        reason += ", including personal work context in a natural way"

    return reason

def create_framework_guidance(framework: Dict, tone_style: Dict, is_persona_based: bool, intent_analysis: Dict) -> str:
    """
    ***MODIFIED***: Create comprehensive, intent-aware, and seamless framework guidance.
    """
    
    intent_type = intent_analysis.get("intent_type", "general")

    # NEW: General instruction for seamless integration
    seamless_instruction = "\n**IMPORTANT**: Your task is to seamlessly weave this structure into the narrative. **DO NOT** use explicit labels like 'Attention:', 'Problem:', or 'Before:' in the final post."

    guidance = f"""
FRAMEWORK: {framework['name']} - {framework['full_name']}
DESCRIPTION: {framework['description']}
{seamless_instruction}

FRAMEWORK-SPECIFIC REQUIREMENTS (MANDATORY FOR '{intent_type.upper()}' INTENT):
"""

    # Add framework-specific guidance that is now aware of the user's intent
    if framework["name"] == "General & Direct Post":
        guidance += """
- **Objective:** Write a clear, concise, and engaging post that directly addresses the user's prompt.
- **Structure:** No complex structure is needed. Focus on a strong, relevant opening, a brief body (2-3 short paragraphs), and a positive, engaging closing.
- **For a celebration prompt:** Focus on the celebratory, positive, and unifying aspects of the event. Keep the tone light and joyful.
"""
    elif framework["name"] == "AIDA":
        if intent_type == "personal":
            guidance += """
- **Attention:** Start with a strong hook about YOUR personal situation or experience.
- **Interest:** Build curiosity by sharing more details about YOUR journey or what YOU are going through.
- **Desire:** Create an emotional connection by showing what this experience means to YOU or what YOU hope to achieve.
- **Action:** End with a clear request related to YOUR personal situation (e.g., asking for connections, support, or shared experiences).
"""
        elif intent_type == "advice":
            guidance += """
- **Attention:** Start with a strong hook that grabs YOUR AUDIENCE'S attention about a problem they face.
- **Interest:** Build THEIR curiosity with useful insights or questions that relate to THEIR goals.
- **Desire:** Create want by showing THEM the benefits of your advice or making an emotional connection to THEIR aspirations.
- **Action:** End with a clear request for THEM to take action or engage with your advice.
"""
        else: # General, Educational, Commentary, Event
            guidance += """
- **Attention:** Start with a strong hook that makes people stop and read.
- **Interest:** Build curiosity with useful insights, facts, or questions.
- **Desire:** Create want or need by showing benefits or making a logical or emotional connection.
- **Action:** End with a clear request for people to engage or reflect.
"""
    # ... (rest of the elif blocks for other frameworks are unchanged from the previous version, so they are omitted for brevity)
    elif framework["name"] == "PAS":
        if intent_type == "personal":
            guidance += """
- **Problem:** Describe a specific problem YOU personally faced.
- **Agitation:** Explain YOUR personal pain points and what happened to YOU because of this problem.
- **Solution:** Share the clear, useful solution that YOU discovered or used to overcome it.
"""
        elif intent_type == "advice":
            guidance += """
- **Problem:** Point out a specific problem YOUR AUDIENCE can relate to.
- **Agitation:** Show THEM the pain points and what happens if nothing changes for THEM.
- **Solution:** Give THEM a clear, useful solution or a way to fix it.
"""
        else: # General, Educational, Commentary
            guidance += """
- **Problem:** Point out a specific problem people can relate to.
- **Agitation:** Show the pain points and what happens if nothing changes.
- **Solution:** Give a clear, useful solution or way to fix it.
"""
    elif framework["name"] == "Before-After-Bridge":
        if intent_type == "personal":
            guidance += """
- **Before:** Describe the difficult situation or problem as YOU experienced it.
- **After:** Paint a picture of the ideal situation that YOU have now reached or are aiming for.
- **Bridge:** Provide the steps or methods that YOU personally used to get from the 'before' to the 'after'.
"""
        elif intent_type == "advice":
            guidance += """
- **Before:** Describe the current problem or difficult situation YOUR AUDIENCE is in.
- **After:** Paint a picture of what the ideal situation would look like for THEM.
- **Bridge:** Provide steps or ways for THEM to get from their problem to the solution.
"""
        else: # General, Educational, Commentary
            guidance += """
- **Before:** Start by describing the current problem or difficult situation.
- **After:** Then paint a picture of what the ideal situation would look like.
- **Bridge:** Finally, provide steps or ways to get from the problem to the solution.
"""
    elif framework["name"] == "Listicle":
        guidance += """
- Create a clear numbered or bulleted list.
- Each point must be useful, valuable, and concise for the target audience.
- Include short, impactful explanations for each point.
- Ensure a strong start and finish that aligns with the user's intent.
"""
    elif framework["name"] == "Question-led":
        # ***MODIFIED: This is the critical fix***
        if intent_type == "personal":
            guidance += """
- **PRIME DIRECTIVE OVERRIDE:** Since this is a personal announcement (like a job search or promotion), you MUST lead with the announcement. The question comes LATER to drive engagement.
- **Hook (The Announcement):** Start with the direct, clear announcement. E.g., "I'm excited to share I am open to new opportunities!"
- **Context:** Briefly provide key details about your value or what you're looking for.
- **Engagement Question:** End the post with a related question to spark discussion. E.g., "What's the best career advice you've ever received?"
- **CRITICAL:** Do NOT start the post with the question. The announcement is the hook.
"""
        else: # Advice, Engagement, Event - This part remains the same
            guidance += """
- Start with a question that makes YOUR AUDIENCE think about a specific topic.
- Give insights that help them answer the question for themselves.
- Ask readers to share their thoughts and opinions.
"""
    elif framework["name"] == "Data-Driven Persuasion":
        if intent_type == "personal":
            guidance += """
- **PRIME DIRECTIVE OVERRIDE:** Lead with the personal announcement. The data comes LATER to support your point.
- **Hook (The Announcement):** Start with the direct, clear announcement.
- **Supporting Data:** Introduce a compelling statistic to add authority to your experience or observation.
- **Action/CTA:** Provide a clear call-to-action based on the insight from the data.
- **CRITICAL:** Do NOT start with the statistic. The announcement is the hook.
"""
        else: # Standard use case
            guidance += """
- **Hook:** Start with one surprising statistic relevant to the user's prompt.
- **Interpretation:** Explain why this data matters to the target audience.
- **Action:** Give one clear, doable step the audience can take based on this data.
"""
    elif framework["name"] == "Credible Spotlight":
        if intent_type == "personal":
            guidance += """
- **PRIME DIRECTIVE OVERRIDE:** YOU are the spotlight. The post must be about your personal milestone.
- **Hook (The Announcement):** Announce your personal milestone or achievement directly.
- **Personal Connection:** Explain why this achievement is meaningful to you.
- **Details:** Share 2-3 specific, impactful details about the journey or accomplishment.
- **Big Picture:** Connect your milestone to a larger goal, lesson, or mission.
- **Engagement:** Ask for reflection or support from readers.
"""
        else: # Standard use case
            guidance += """
- **Credibility:** Feature a real person, event, or milestone.
- **Personal Connection:** Explain why this matters to the industry or community.
- **Details:** Share 2-3 specific, impactful things about their achievements.
- **Big Picture:** Connect this spotlight to a larger cause, mission, or lesson.
"""
    elif framework["name"] == "Counterintuitive Leadership Truth":
        if intent_type == "personal":
            guidance += """
- **PRIME DIRECTIVE OVERRIDE:** Lead with your personal announcement. The contrarian insight can be a supporting theme later in the post.
- **Hook (The Announcement):** Start with your direct, personal announcement.
- **Insight:** Connect your announcement to a counterintuitive lesson you learned along the way.
- **Better Alternative:** Explain how this new perspective is more effective.
- **Practical Step:** Give one piece of advice based on your experience.
"""
        else: # Standard use case
            guidance += """
- **Contrarian Thesis:** Challenge a common belief with a surprising insight.
- **Why It Fails:** Explain why the old belief doesn't work, providing examples.
- **Better Alternative:** Present a new, more effective approach.
- **Practical Step:** Give the audience one thing they can try right away.
"""

    return guidance


def select_post_with_url(posts: List[str], query: str) -> Dict[str, Any]:
    """
    Select the best post to pair with a URL and fetch the URL.
    This function now correctly handles the async call to fetch_related_url.
    """
    try:
        posts_with_media = []
        for post in posts:
            media_analysis = analyze_post_for_media(post)
            posts_with_media.append({
                "content": post,
                "has_image": media_analysis["has_image"],
                "has_infographics": media_analysis["has_infographics"]
            })

        selected_index = select_best_post(posts_with_media)
        selected_post_content = posts[selected_index]

        # Run the async function and wait for its result safely whether loop exists or not
        try:
            loop = asyncio.get_running_loop()
            # If we're already inside an event loop, schedule a task and wait for it
            url = loop.run_until_complete(fetch_related_url(selected_post_content)) if not loop.is_running() else None
            if url is None:
                # Fallback: create a new task and wait synchronously via asyncio.run in a thread-safe way
                url = None
        except RuntimeError:
            # No running loop; safe to use asyncio.run
            url = asyncio.run(fetch_related_url(selected_post_content))

        return {
            "selected_index": selected_index,
            "url": url
        }
    except Exception as e:
        print(f"Error selecting post with URL: {str(e)}")
        # It's better to log the exception for debugging
        logging.error(f"Error in select_post_with_url: {e}")
        return {
            "selected_index": 0,
            "url": None
        }

@lru_cache(maxsize=500)
def analyze_post_for_media(post_content: str) -> dict:
    """Analyze a post to determine if it would benefit from images or infographics.
    
    Args:
        post_content: The content of the post
        
    Returns:
        Dictionary with has_image and has_infographics flags
    """
    try:
        # Simple heuristic-based analysis
        content_lower = post_content.lower()
        
        # Check for data/statistics indicators (more specific)
        data_indicators = [
            'percent', '%', 'statistics', 'data', 'comparison', 'chart', 'graph',
            'numbers', 'figures', 'analysis', 'survey', 'study', 'research',
            'increase', 'decrease', 'growth', 'decline', 'trend', 'survey results',
            'market share', 'revenue', 'profit', 'cost', 'efficiency', 'performance metrics'
        ]
        
        # Check for visual content indicators (more specific)
        visual_indicators = [
            'image', 'photo', 'picture', 'visual', 'design', 'layout',
            'color', 'brand', 'logo', 'icon', 'illustration', 'screenshot',
            'before and after', 'comparison image', 'product image', 'team photo'
        ]
        
        # Check for process/step indicators (more specific)
        process_indicators = [
            'step', 'process', 'workflow', 'pipeline', 'framework',
            'methodology', 'approach', 'strategy', 'plan', 'roadmap',
            'timeline', 'phases', 'stages', 'cycle', 'lifecycle'
        ]
        
        # Count occurrences to determine strength of indicators
        data_count = sum(1 for indicator in data_indicators if indicator in content_lower)
        visual_count = sum(1 for indicator in visual_indicators if indicator in content_lower)
        process_count = sum(1 for indicator in process_indicators if indicator in content_lower)
        
        # Determine media recommendations with more nuanced logic
        has_strong_data = data_count >= 3  # Increased threshold - need more data indicators
        has_strong_visual = visual_count >= 2  # Need multiple visual indicators
        has_strong_process = process_count >= 3  # Increased threshold - need more process indicators
        
        # Content length consideration
        content_length = len(post_content)
        is_long_content = content_length > 1000
        
        # Determine media type based on content analysis
        has_infographics = False
        has_image = False
        
        # Priority 1: Strong data indicators suggest infographics
        if has_strong_data:
            has_infographics = True
        # Priority 2: Strong visual indicators suggest images
        elif has_strong_visual:
            has_image = True
        # Priority 3: Strong process indicators suggest infographics
        elif has_strong_process:
            has_infographics = True
        # Priority 4: Long content without specific indicators might benefit from images
        elif is_long_content and not (has_strong_data or has_strong_visual or has_strong_process):
            has_image = True
        # Priority 5: Only consider infographics if we have multiple data indicators (at least 2)
        elif data_count >= 2 and not has_strong_visual and not has_strong_process:
            has_infographics = True
        # Priority 6: If we have some visual indicators but not strong enough, still consider images
        elif visual_count >= 1 and not has_strong_data and not has_strong_process:
            has_image = True
        
        # Ensure mutual exclusivity
        if has_infographics and has_image:
            # If both are true, prioritize based on content type
            if has_strong_data or has_strong_process:
                has_image = False
                has_infographics = True
            elif has_strong_visual:
                has_image = True
                has_infographics = False
            else:
                # Default to infographics for data-heavy content
                has_image = False
                has_infographics = True
        
        return {
            "has_image": has_image,
            "has_infographics": has_infographics
        }
    except Exception as e:
        print(f"Error analyzing post for media: {str(e)}")
        return {
            "has_image": False,
            "has_infographics": False
        }

def select_best_post(posts_with_media):
    """Select the best post from a list of posts with media analysis.
    
    Args:
        posts_with_media: List of dictionaries with post content and media analysis
        
    Returns:
        Index of the best post
    """
    try:
        # Simple selection logic - prefer posts with infographics, then images
        best_index = 0
        best_score = 0
        
        for i, post_data in enumerate(posts_with_media):
            score = 0
            if post_data.get("has_infographics"):
                score += 3
            if post_data.get("has_image"):
                score += 2
            if len(post_data.get("content", "")) > 1000:
                score += 1
                
            if score > best_score:
                best_score = score
                best_index = i
                
        return best_index
    except Exception as e:
        print(f"Error selecting best post: {str(e)}")
        return 0

def generate_post_from_persona_keywords(general_persona_keywords, tone, style, user_prompt, content_interests=None, network_interests=None, add_emojis=False, add_hashtags=True, use_hook_generator=True, used_opening_words=None, include_tone_in_response=False):
    """
    ***MODIFIED***: The final version with the strictest prompt adherence instructions.
    This version explicitly forbids placeholders and invented stories, and forces the AI
    to treat the user's prompt as the ultimate source of truth.
    """
    logger = logging.getLogger(__name__)
    logger.info("Starting post generation with enhanced randomization and three-variant system")
    
    if used_opening_words is None:
        used_opening_words = set()
    
     # --- INTELLIGENT CONTEXT ANALYSIS (This part is already correct) ---
    framework_analysis = classify_prompt_and_select_frameworks(user_prompt, general_persona_keywords)
    selected_frameworks = framework_analysis["selected_frameworks"]
    is_persona_based = framework_analysis["is_persona_based"]
    detected_intent = framework_analysis["intent"]
    intent_analysis = framework_analysis.get("intent_analysis", {})
    content_complexity = intent_analysis.get("content_complexity", "direct_announcement")
    sentiment = intent_analysis.get("sentiment", "neutral")

    is_persona_based = framework_analysis.get("is_persona_based", False)
    intent_analysis = framework_analysis.get("intent_analysis", {})

    # --- CRITICAL FIX FOR GENERIC PROMPTS ---
    # If no prompt is provided, force the complexity to be short and direct.
    if not user_prompt:
        logger.info("No user prompt provided. Forcing 'direct_announcement' complexity for generic post.")
        intent_analysis['content_complexity'] = 'direct_announcement'
    
    content_complexity = intent_analysis.get("content_complexity", "direct_announcement")
    sentiment = intent_analysis.get("sentiment", "neutral")

    # Apply intelligent tone assignment to frameworks
    try:
        selected_frameworks = intelligent_tone_assignment(selected_frameworks, user_prompt, intent_analysis)
        logger.info(f"Applied intelligent tone assignment: {[(fw['name'], fw.get('assigned_tone', 'Professional')) for fw in selected_frameworks]}")
    except Exception as e:
        logger.warning(f"Error in tone assignment, using fallback: {str(e)}")

    # Generate 3 distinctly different variant posts
    def generate_variant(i):
        logger.info(f"Generating variant {i+1} with AI LinkedIn Expert framework selection")
        

        framework = selected_frameworks[i]
        assigned_tone_style = {
            "tone": framework.get("assigned_tone", "Professional"),
            "style": ", ".join(framework.get("style_attributes", [])),
            "approach": framework.get("approach_guidance", ""),
            "voice": framework.get("voice_characteristics", ""),
            "description": framework.get("tone_description", "")
        }
        framework_reason = generate_framework_reason(framework, detected_intent, is_persona_based, user_prompt)
        framework_guidance = create_framework_guidance(framework, assigned_tone_style, is_persona_based, intent_analysis)
        persona_section = ""
        if is_persona_based:
            # ... (persona section logic is unchanged) ...
            general_persona_keywords_str = ", ".join(general_persona_keywords)
            persona_section = f"""
---
### 👤 PERSONA CONTEXT (Use this to inform the post's content and voice)
-   **General Keywords:** {general_persona_keywords_str}
"""
        else:
            persona_section = "\n-   **Persona Context:** NOT APPLICABLE."
        
        # --- NEW: DYNAMIC LENGTH INSTRUCTION IS NOW AUTOMATIC ---
        length_instruction = ""
        if content_complexity == "direct_announcement":
            length_instruction = """
---
### 📏 POST LENGTH & STYLE: SHORT & DIRECT

**CRITICAL STYLE NOTE:** Keep the post very short and to the point (approx. 3-5 sentences). Focus on making a clear, confident announcement. Prioritize brevity and scannability above detailed strategic arguments. The goal is a quick, authentic update.
"""
        else: # "strategic_narrative"
            length_instruction = """
---
### 📏 POST LENGTH & STYLE: STRATEGIC & DETAILED

**STYLE NOTE:** Create a well-structured, persuasive post. While maintaining scannability, develop the core idea using the framework to build a compelling narrative or argument. The goal is maximum impact and persuasion.
"""

# --- NEW: THE NEGATIVE PROMPT REFRAMING GUARDRAIL ---
        reframing_instruction = ""
        if sentiment == 'negative':
            reframing_instruction = """
---
### ⚠️ SPECIAL HANDLING INSTRUCTIONS: NEGATIVE PROMPT DETECTED

The user's prompt is unprofessional and negative. Your primary task is to **reframe** this raw emotion into a constructive and professional LinkedIn post.

1.  **DO NOT REPEAT THE NEGATIVE LANGUAGE:** Absolutely do not use words like "hate," "bossy," "sucks," or any insults from the user's prompt.
2.  **IDENTIFY THE UNDERLYING PROFESSIONAL THEME:** Analyze the user's frustration and extract the core professional topic. For example:
    -   "I hate my boss" -> The theme is "the importance of good leadership" or "navigating difficult management styles."
    -   "My company sucks" -> The theme is "the key elements of a positive work culture" or "seeking a new environment that values its employees."
3.  **WRITE ABOUT THE REFRAMED THEME:** Your entire post must be about the new, constructive theme you identified. Frame it as a thoughtful reflection, a piece of advice for others, or an insight into what makes a great workplace.
4.  **MAINTAIN A PROFESSIONAL TONE:** The final output must be 100% professional and suitable for a broad LinkedIn audience.
"""


        # --- THE NEW, FULLY REFINED MASTER PROMPT ---
        prompt = f"""
You are a World-Class LinkedIn Ghostwriter and Strategic Communications Expert. Your mission is to generate a single, outstanding LinkedIn post based *strictly* on the user's prompt and the provided context.

{reframing_instruction}

---
### 👑 THE GOLDEN RULE: BE CONCISE AND MEANINGFUL (YOUR HIGHEST PRIORITY)

This is your most important instruction. It overrides all other rules.

1.  **Brevity is Everything:** Your primary goal is to write the SHORTEST possible post that still delivers the user's message with impact. Ruthlessly cut any sentence or word that is not essential. "Meaningful" means delivering value efficiently.
2.  **Never Be Large:** No post, for any reason, should feel "large" or "long." Even a `strategic_narrative` must be concise and structured, not a long essay. If a framework's structure is making the post too long, simplify the framework.
3.  **Adherence Over Creativity:** You MUST follow all formatting and structural rules (like HTML lists and single-question CTAs) exactly as written. Adherence to these rules is more important than creative freedom.
---
### 👑 META-DIRECTIVE: THE RULE OF OVERRIDES (MOST IMPORTANT RULE)

This is your highest-level instruction. It overrides all other rules if there is a conflict.

-   **If the user's intent is an ANNOUNCEMENT** (like a `personal_story` about a job search, or an `event_announcement`), then the **Pinpoint Announcement Hook** rule (from the ✨ POST ANATOMY section) is your absolute, non-negotiable priority. It **OVERRIDES** any conflicting structural suggestions from the selected Framework Guidance.
-   **Example:** If the framework is 'Data-Driven' but the intent is a job search, you MUST lead with the job search announcement. You MUST NOT lead with a statistic. You can then weave the data in later if it's relevant. The announcement always comes first.

---
### 🚨 THE PRIME DIRECTIVE: The User's Prompt is the Ultimate Source of Truth

Your absolute #1 priority is to fulfill the user's request with maximum relevance and precision.

1.  **Strict Adherence:** The content of your post MUST directly address the user's prompt. Do not deviate or introduce unrelated topics. For a prompt like "aws vs gcp," the post must be about comparing aws and gcp.
2.  **Frameworks are Tools, Not Masters:** The selected framework is a *guide* for structure, not a rigid cage. You MUST adapt the framework to perfectly fit the user's topic. If a framework's structure feels unnatural for the prompt (e.g., a complex framework for a simple celebration), simplify your approach and write a more direct post. **Never force a topic into a framework awkwardly.**
3.  **Objective First:** For announcements (job searches, celebrations, etc.), the main point MUST be in the first one or two lines.

---
### ⛔ CRITICAL BOUNDARIES & CONSTRAINTS (NON-NEGOTIABLE)

1.  **NO PLACEHOLDERS:** You are strictly forbidden from using placeholders like "[Company Name]", "[Your Name]", "[Client Name]", "[Event Name]", etc. If a specific name is not provided in the prompt, you MUST write the post in a general way that does not require it.
2.  **NO INVENTED STORIES OR PEOPLE:** For any intent that is NOT a `personal_story`, you MUST write from an objective, professional perspective. Do NOT invent characters, anecdotes, or fictional stories (e.g., a story about a person named "Sara"). Stick strictly to the facts and the topic of the user's prompt.
3.  **NO EXTERNAL LINKS:** Do not invent or add any external URLs, "learn more" links, or phrases like "link in comments."

---
### ✨ POST ANATOMY & STYLE

1.  **Brutal Brevity:** Every post must be as short as possible while still being impactful. Ruthlessly cut any sentence that does not directly support the user's core objective. For announcements, 3-5 short paragraphs is the absolute maximum.
2.  **Design for Scanners:** Use whitespace and short paragraphs.
3.  **The Bold, Pinpoint Hook with True Conceptual Variety (Your Most Important Creative Task):**
    -   The first sentence of the post is the hook. It MUST be **pinpoint-relevant** to the user's prompt and wrapped in `<strong>...</strong>` tags.
    -   As per the Meta-Directive, if the intent is an announcement, the hook **IS** the announcement itself.
    -   **CRITICAL CREATIVE MODEL:** To achieve true conceptual variety, you MUST frame each of the three hooks around a different core element of the user's message. You will generate the three variants by applying these three distinct frames:
        -   **Variant 1 Hook - The "What" Frame (Direct & Energetic):** Focus purely on the news itself. Announce **WHAT** is happening with energy and excitement. This is the most direct and to-the-point hook.
        -   **Variant 2 Hook - The "Why" Frame (Reflective & Story-Driven):** Focus on the journey or reason **WHY** this is happening. Frame the news as the next logical step in a story or a reflection on the past.
        -   **Variant 3 Hook - The "Value" Frame (Forward-Looking & Impact-Oriented):** Focus on the future impact or the **VALUE** being offered. Frame the news in terms of what it means for others or what you will contribute.

    -   This three-frame model is mandatory for all prompts to ensure true variety.
4.  **Design for Scanners:** Use extreme whitespace and short paragraphs.
{length_instruction}

---
### 🏛️ RICH TEXT HTML FORMATTING (NEW, FINAL VERSION WITH EMOJI BULLETS)

You MUST use HTML for rich text formatting to make the post visually engaging and easy to read.
-   Wrap all paragraphs in `<p>` tags.
-   Use `<strong>` or `<b>` to emphasize 1-3 key phrases or concepts that deserve the reader's attention.
-   For lists of items, tips, or points, you MUST use bulleted lists (`<ul><li>...</li></ul>`). **Inside each `<li>` tag, you MUST start the point with a relevant and professional emoji bullet** (e.g., `<li>✅ Your Point Here</li>`, `<li>🚀 Your Point Here</li>`, `<li>💡 Your Point Here</li>`). Do not use manual dashes or numbers.
-   For quotes, use `<blockquote>...</blockquote>`.
-   For inline code or technical terms, use `<code>...</code>`.
-   For numbered steps or rankings, use `<ol><li>...</li></ol>`.
---
### 🎯 CLIENT OBJECTIVE & CONTEXT
-   **Intent Type:** {intent_analysis.get("intent_type", "general").upper()}
-   **Content Complexity:** {content_complexity.upper()}
{ "👤 PERSONA CONTEXT: " + ", ".join(general_persona_keywords) if is_persona_based else "👤 PERSONA CONTEXT: Not Applicable. Write from a general professional perspective." }

---
### 📝 FRAMEWORK GUIDANCE
{create_framework_guidance(framework, {}, is_persona_based, intent_analysis)}

---
### 📜 USER'S PROMPT
**User Prompt:** {user_prompt if user_prompt else "Generate professional content based on general knowledge. Since no prompt is provided, create a post about a trending topic in technology or business."}

---
### ✅ FINAL INSTRUCTIONS
1.  **Generate the complete post in rich text HTML format, following all formatting rules.**
2.  Adhere to all Prime Directives and Critical Boundaries.
3.  Ensure the post is 100% relevant to the user's prompt.
4.**INTELLIGENT CTA RULE:** The post MUST include a natural, contextually appropriate call-to-action that fits seamlessly with the content. This CTA should be integrated naturally within the post flow - it can be part of the main content, a concluding thought, or a final engaging question. DO NOT add a separate, forced CTA paragraph if the content already contains a natural call-to-action (such as "I'd love to connect!", "Let's discuss this", "What are your thoughts?", etc.). The goal is ONE cohesive, natural CTA that matches the post's intent and tone.
"""
        
        from app.utils.model_initializer import model

        logger.info(f"Variant {i+1} ({framework['name']}) using {assigned_tone_style['tone']} tone")
        
        response = model.generate_content(prompt)
        generated_content = response.text.strip()

        # Clean up any markdown formatting with proper error handling
        if "```html" in generated_content:
            parts = generated_content.split("```html", 1)
            if len(parts) > 1:
                sub_parts = parts[1].split("```", 1)
                if len(sub_parts) > 0:
                    generated_content = sub_parts[0].strip()
        elif "```" in generated_content:
            parts = generated_content.split("```", 1)
            if len(parts) > 1:
                generated_content = parts[1].strip()

        generated_content = generated_content.replace("```html", "").replace("```", "").strip()
        
        def remove_inline_hashtags(text):
            if "<p class='hashtags'>" in text:
                main_part, hashtags_part = text.split("<p class='hashtags'>", 1)
                main_part = re.sub(r'#\w+\s*', '', main_part)
                return main_part + "<p class='hashtags'>" + hashtags_part
            else:
                return re.sub(r'#\w+\s*', '', text)

        generated_content = remove_inline_hashtags(generated_content).strip()

        if add_emojis:
            try:
                # Conditionally pass persona keywords for emoji generation
                emoji_persona_keywords = general_persona_keywords if is_persona_based else None
                generated_content = add_intelligent_emojis_to_post(generated_content, emoji_persona_keywords, content_interests)
            except Exception as e:
                print(f"Failed to add emojis: {e}")

        if add_hashtags:
            hashtags = generate_hashtags_for_post(generated_content, content_interests)
            if hashtags:
                # CRITICAL FIX: Always wrap hashtags in their own <p> tag.
                # This creates the necessary line break after the CTA question.
                if "<p class='hashtags'>" not in generated_content:
                    generated_content += f"\n<p class='hashtags'>{hashtags}</p>"
        return {
            "content": generated_content,
            "framework": framework["name"],
            "reason": framework_reason,
            "tone": assigned_tone_style["tone"]
        }

    # Generate all 3 variants using parallel processing
    posts = []
    try:
        with concurrent.futures.ThreadPoolExecutor(max_workers=3, thread_name_prefix="PostGen") as executor:
            future_to_index = {executor.submit(generate_variant, i): i for i in range(3)}
            for future in concurrent.futures.as_completed(future_to_index, timeout=120):
                variant_index = future_to_index[future]
                try:
                    variant_result = future.result(timeout=60)
                    if variant_result:
                        posts.append((variant_index, variant_result))
                        logger.info(f"Successfully generated variant {variant_index + 1}")
                except Exception as e:
                    logger.error(f"Error generating variant {variant_index + 1}: {e}")
    except Exception as e:
        logger.error(f"Error in parallel post generation setup: {e}")
        logger.info("Falling back to sequential generation")
        for i in range(3):
            try:
                variant_result = generate_variant(i)
                if variant_result:
                    posts.append((i, variant_result))
            except Exception as seq_e:
                logger.error(f"Error in sequential fallback for variant {i + 1}: {seq_e}")

    posts.sort(key=lambda x: x[0])

    response_posts = []
    for i, post_data in posts:
        post_obj = {
            "content": post_data["content"],
            "framework": post_data["framework"],
            "reason": post_data["reason"]
        }
        media_analysis = analyze_post_for_media(post_data["content"])
        post_obj["has_image"] = media_analysis.get("has_image", False)
        post_obj["has_infographics"] = media_analysis.get("has_infographics", False)

        if include_tone_in_response and post_data.get("tone"):
            post_obj["tone"] = post_data.get("tone")
        response_posts.append(post_obj)

    # Add fallback posts if generation fails
    while len(response_posts) < 3:
        fallback_index = len(response_posts)
        try:
            fallback_result = generate_variant(fallback_index)
            # CRITICAL FIX: Validate fallback_result before accessing its properties
            if fallback_result is None:
                logger.error(f"Fallback variant {fallback_index+1} returned None")
                break
            if not isinstance(fallback_result, dict):
                logger.error(f"Fallback variant {fallback_index+1} returned invalid type: {type(fallback_result)}")
                break
            if "content" not in fallback_result:
                logger.error(f"Fallback variant {fallback_index+1} missing content key")
                break

            media_analysis = analyze_post_for_media(fallback_result["content"])
            post_obj = {
                "content": fallback_result["content"],
                "has_image": media_analysis.get("has_image", False),
                "has_infographics": media_analysis.get("has_infographics", False),
                "framework": fallback_result.get("framework", "Unknown"),
                "reason": fallback_result.get("reason", "Fallback generation")
            }
            response_posts.append(post_obj)
        except Exception as e:
            logger.error(f"Error generating fallback variant {fallback_index+1}: {e}")
            break

    # Add URL metadata to the best post
    if response_posts:
        final_post_contents = [post["content"] for post in response_posts]
        url_result = select_post_with_url(final_post_contents, user_prompt or "")
        if url_result.get("url"):
            best_post_index = url_result.get("selected_index", 0)
            if best_post_index < len(response_posts):
                response_posts[best_post_index]["url"] = url_result["url"]

    # FINAL SAFETY CHECK: Never return empty posts array
    if not response_posts:
        logger.error("All post generation attempts failed, creating emergency fallback post")
        emergency_post = {
            "content": f"<p>Professional insight about {user_prompt[:100] if user_prompt else 'your industry'}...</p><p>I'd love to hear your thoughts on this topic. What's your experience?</p>",
            "has_image": False,
            "has_infographics": False,
            "framework": "Emergency",
            "reason": "Emergency fallback due to generation failure"
        }
        response_posts.append(emergency_post)

    logger.info(f"Returning {len(response_posts)} posts")
    return {"posts": response_posts}